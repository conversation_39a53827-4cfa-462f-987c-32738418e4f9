import { useState, useCallback } from "react";
import { imageUrl } from "@/utils/image-url";

// All images used throughout the game experience
const GAME_IMAGES = [
  // Background images
  "/screen-login.png",
  "/screen-game-entries.png",
  "/screen-game.png",
  "/game-instruction-dialog.png",
  "/game-result-background.png",

  // Game entry buttons
  "/btn-game1-play-now.png",
  "/btn-game1-play-again.png",
  "/btn-game2-play-now.png",
  "/btn-game2-play-again.png",
  "/btn-game2-coming-soon.png",
  "/btn-game3-play-now.png",
  "/btn-game3-play-again.png",
  "/btn-game3-coming-soon.png",

  // Game titles
  "/game-title-balance.png",
  "/game-title-catch.png",
  "/game-title-quiz.png",

  // Balance game assets
  "/game-score-board.png",
  "/game-balance-cap.png",
  "/game-balance-direction.png",
  "/game-balance-water.png",

  // UI buttons
  "/button-app.png",
  "/button-buy.png",
  "/button-wide.png",

  // Dialog images
  "/popup-luckydraw-reward.png",
  "/popup-normal.png",
  "/game-catch-asset-3.png",
];

interface PreloaderState {
  isLoading: boolean;
  progress: number;
  loadedCount: number;
  totalCount: number;
  isComplete: boolean;
  hasStarted: boolean;
  startTime: number;
}

export const useImagePreloader = () => {
  const [state, setState] = useState<PreloaderState>({
    isLoading: false,
    progress: 0,
    loadedCount: 0,
    totalCount: GAME_IMAGES.length,
    isComplete: false,
    hasStarted: false,
    startTime: 0,
  });

  const preloadImages = useCallback(() => {
    const startTime = Date.now();
    setState((prev) => {
      if (prev.hasStarted) return prev; // Prevent multiple starts
      return {
        ...prev,
        isLoading: true,
        isComplete: false,
        hasStarted: true,
        startTime,
      };
    });

    let loadedCount = 0;
    const totalCount = GAME_IMAGES.length;
    const MIN_LOADING_TIME = 1500; // Minimum 1.5 seconds

    const checkCompletion = () => {
      const elapsedTime = Date.now() - startTime;
      const allImagesLoaded = loadedCount === totalCount;
      const minTimeElapsed = elapsedTime >= MIN_LOADING_TIME;

      if (allImagesLoaded && minTimeElapsed) {
        setState((prev) => ({
          ...prev,
          isComplete: true,
          isLoading: false,
        }));
      } else if (allImagesLoaded && !minTimeElapsed) {
        // Wait for minimum time to elapse
        setTimeout(() => {
          setState((prev) => ({
            ...prev,
            isComplete: true,
            isLoading: false,
          }));
        }, MIN_LOADING_TIME - elapsedTime);
      }
    };

    const updateProgress = () => {
      loadedCount++;
      const progress = Math.round((loadedCount / totalCount) * 100);

      setState((prev) => ({
        ...prev,
        loadedCount,
        progress,
      }));

      checkCompletion();
    };

    // Preload all images
    GAME_IMAGES.forEach((imagePath) => {
      const img = new Image();

      img.onload = updateProgress;
      img.onerror = updateProgress; // Still count as "loaded" to prevent hanging

      img.src = imageUrl(imagePath);
    });
  }, []);

  return {
    ...state,
    preloadImages,
  };
};
